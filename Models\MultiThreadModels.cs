using System.ComponentModel;

namespace AWSAutoRegister.Models
{
    /// <summary>
    /// 线程状态枚举
    /// </summary>
    public enum ThreadStatus
    {
        NotStarted,              // 未开始
        Initializing,            // 初始化中
        Processing,              // 处理中
        WaitingForManualAction,  // 等待手动操作
        Paused,                  // 暂停
        Completed,               // 已完成
        CompletedWithIssues,     // 注册完成但有问题（如账单问题）
        Failed,                  // 失败
        Terminated               // 已终止
    }

    /// <summary>
    /// 线程状态信息
    /// </summary>
    public class ThreadStatusInfo : INotifyPropertyChanged
    {
        private ThreadStatus _status = ThreadStatus.NotStarted;
        private string _message = string.Empty;
        private string _title = string.Empty;
        private bool _needsUserAction = false;
        private int _progress = 0;
        private string _currentOperation = string.Empty;

        public ThreadStatus Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged(nameof(Status));
                OnPropertyChanged(nameof(StatusText));
            }
        }

        public string Message
        {
            get => _message;
            set
            {
                _message = value;
                OnPropertyChanged(nameof(Message));
            }
        }

        public string Title
        {
            get => _title;
            set
            {
                _title = value;
                OnPropertyChanged(nameof(Title));
            }
        }

        public bool NeedsUserAction
        {
            get => _needsUserAction;
            set
            {
                _needsUserAction = value;
                OnPropertyChanged(nameof(NeedsUserAction));
            }
        }

        public int Progress
        {
            get => _progress;
            set
            {
                _progress = Math.Max(0, Math.Min(100, value));
                OnPropertyChanged(nameof(Progress));
            }
        }

        public string CurrentOperation
        {
            get => _currentOperation;
            set
            {
                _currentOperation = value;
                OnPropertyChanged(nameof(CurrentOperation));
            }
        }

        public string StatusText => Status switch
        {
            ThreadStatus.NotStarted => "未开始",
            ThreadStatus.Initializing => "初始化中",
            ThreadStatus.Processing => "处理中",
            ThreadStatus.WaitingForManualAction => "等待手动操作",
            ThreadStatus.Paused => "已暂停",
            ThreadStatus.Completed => "已完成",
            ThreadStatus.CompletedWithIssues => "注册完成",
            ThreadStatus.Failed => "失败",
            ThreadStatus.Terminated => "已终止",
            _ => "未知状态"
        };

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// 手机号码信息
    /// </summary>
    public class PhoneNumberInfo
    {
        public string FullNumber { get; set; } = string.Empty;
        public string CountryCode { get; set; } = string.Empty;
        public string LocalNumber { get; set; } = string.Empty;
        public DateTime AssignedTime { get; set; } = DateTime.Now;
        public int AssignedThreadId { get; set; } = 0;
        public bool IsUsed { get; set; } = false;
    }

    /// <summary>
    /// 窗口位置信息
    /// </summary>
    public class WindowPosition
    {
        public int X { get; set; }
        public int Y { get; set; }
        public int Width { get; set; } = 500;
        public int Height { get; set; } = 500;
    }

    /// <summary>
    /// 浏览器指纹信息
    /// </summary>
    public class BrowserFingerprint
    {
        public string UserAgent { get; set; } = string.Empty;
        public string TimeZone { get; set; } = string.Empty;
        public string Language { get; set; } = "zh-CN,zh;q=0.9,en;q=0.8";
        public string CountryCode { get; set; } = string.Empty; // 国家代码
        public (double Latitude, double Longitude)? Geolocation { get; set; } // 地理位置
        public DateTime CreatedTime { get; set; } = DateTime.Now;
    }

    /// <summary>
    /// 数据状态枚举（扩展现有的DataProcessStatus）
    /// </summary>
    public enum DataStatus
    {
        Unprocessed,    // 未处理
        Assigned,       // 已分配给线程
        Processing,     // 处理中
        Completed,      // 已完成
        Failed,         // 失败
        Released        // 已释放
    }

    /// <summary>
    /// 线程数据队列项
    /// </summary>
    public class ThreadDataItem
    {
        public RegistrationData Data { get; set; } = new RegistrationData();
        public int AssignedThreadId { get; set; } = 0;
        public DateTime AssignedTime { get; set; } = DateTime.Now;
        public DataStatus Status { get; set; } = DataStatus.Unprocessed;
        public string ProcessingNotes { get; set; } = string.Empty;
    }

    /// <summary>
    /// 验证码请求信息
    /// </summary>
    public class VerificationRequest
    {
        public string PhoneNumber { get; set; } = string.Empty;
        public int ThreadId { get; set; } = 0;
        public DateTime RequestTime { get; set; } = DateTime.Now;
        public TaskCompletionSource<(bool Success, string? Code, string Message)> TaskSource { get; set; } = new();
    }

    /// <summary>
    /// 批次状态枚举
    /// </summary>
    public enum BatchState
    {
        Collecting,     // 收集阶段：等待线程完成第六页
        Processing,     // 处理阶段：正在获取验证码（包含5秒等待）
        Idle           // 空闲阶段：可以开始新批次
    }

    /// <summary>
    /// 线程安全数据管理器的配置
    /// </summary>
    public class ThreadSafeDataConfig
    {
        public int MaxThreadCount { get; set; } = 6;
        public TimeSpan DataAssignmentTimeout { get; set; } = TimeSpan.FromMinutes(5);
        public bool EnableDataValidation { get; set; } = true;
        public string LogFilePath { get; set; } = "awstool_log.txt";
    }
}
