using System;
using AWSAutoRegister.Models;
using AGaws单线程版.Services;

namespace AWSAutoRegister.Services
{
    /// <summary>
    /// 浏览器指纹适配器 - 在不同命名空间的BrowserFingerprint类之间进行转换
    /// </summary>
    public static class BrowserFingerprintAdapter
    {
        /// <summary>
        /// 生成完整的浏览器指纹（AWSAutoRegister.Models版本）
        /// </summary>
        public static void GenerateAdvancedFingerprint(BrowserFingerprint fingerprint)
        {
            // 创建AGaws单线程版.Models版本的指纹
            var tempFingerprint = new AGaws单线程版.Models.BrowserFingerprint
            {
                UserAgent = fingerprint.UserAgent,
                TimeZone = fingerprint.TimeZone,
                Language = fingerprint.Language,
                CountryCode = fingerprint.CountryCode,
                Geolocation = fingerprint.Geolocation,
                CreatedTime = fingerprint.CreatedTime
            };

            // 使用原始服务生成指纹
            BrowserFingerprintService.GenerateAdvancedFingerprint(tempFingerprint);

            // 复制回来
            fingerprint.CanvasFingerprint = tempFingerprint.CanvasFingerprint;
            fingerprint.WebGLFingerprint = tempFingerprint.WebGLFingerprint;
            fingerprint.AudioContextFingerprint = tempFingerprint.AudioContextFingerprint;
            fingerprint.MediaDevicesFingerprint = tempFingerprint.MediaDevicesFingerprint;
            fingerprint.ClientRectsFingerprint = tempFingerprint.ClientRectsFingerprint;
            fingerprint.SpeechVoicesFingerprint = tempFingerprint.SpeechVoicesFingerprint;
            fingerprint.WebGLRenderer = tempFingerprint.WebGLRenderer;
            fingerprint.WebGLVendor = tempFingerprint.WebGLVendor;
            fingerprint.CPUCores = tempFingerprint.CPUCores;
            fingerprint.RAMSize = tempFingerprint.RAMSize;
            fingerprint.DeviceName = tempFingerprint.DeviceName;
            fingerprint.MACAddress = tempFingerprint.MACAddress;
            fingerprint.DoNotTrack = tempFingerprint.DoNotTrack;
            fingerprint.PortScanProtection = tempFingerprint.PortScanProtection;
            fingerprint.HardwareAcceleration = tempFingerprint.HardwareAcceleration;
            fingerprint.TLSFeatures = tempFingerprint.TLSFeatures;
        }

        /// <summary>
        /// 生成JavaScript指纹注入脚本（AWSAutoRegister.Models版本）
        /// </summary>
        public static string GenerateFingerprintScript(BrowserFingerprint fingerprint)
        {
            // 创建AGaws单线程版.Models版本的指纹
            var tempFingerprint = new AGaws单线程版.Models.BrowserFingerprint
            {
                UserAgent = fingerprint.UserAgent,
                TimeZone = fingerprint.TimeZone,
                Language = fingerprint.Language,
                CountryCode = fingerprint.CountryCode,
                Geolocation = fingerprint.Geolocation,
                CreatedTime = fingerprint.CreatedTime,
                CanvasFingerprint = fingerprint.CanvasFingerprint,
                WebGLFingerprint = fingerprint.WebGLFingerprint,
                AudioContextFingerprint = fingerprint.AudioContextFingerprint,
                MediaDevicesFingerprint = fingerprint.MediaDevicesFingerprint,
                ClientRectsFingerprint = fingerprint.ClientRectsFingerprint,
                SpeechVoicesFingerprint = fingerprint.SpeechVoicesFingerprint,
                WebGLRenderer = fingerprint.WebGLRenderer,
                WebGLVendor = fingerprint.WebGLVendor,
                CPUCores = fingerprint.CPUCores,
                RAMSize = fingerprint.RAMSize,
                DeviceName = fingerprint.DeviceName,
                MACAddress = fingerprint.MACAddress,
                DoNotTrack = fingerprint.DoNotTrack,
                PortScanProtection = fingerprint.PortScanProtection,
                HardwareAcceleration = fingerprint.HardwareAcceleration,
                TLSFeatures = fingerprint.TLSFeatures
            };

            // 使用原始服务生成脚本
            return BrowserFingerprintService.GenerateFingerprintScript(tempFingerprint);
        }
    }
}
