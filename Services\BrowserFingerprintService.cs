using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using AWSAutoRegister.Models;

namespace AWSAutoRegister.Services
{
    /// <summary>
    /// 浏览器指纹服务 - 负责生成指纹数据和JavaScript注入脚本
    /// </summary>
    public static class BrowserFingerprintService
    {
        private static readonly Random _random = new Random();

        // Canvas指纹数据池（至少10个选项）
        private static readonly string[] CanvasFingerprintPool = new[]
        {
            "canvas_fp_001", "canvas_fp_002", "canvas_fp_003", "canvas_fp_004", "canvas_fp_005",
            "canvas_fp_006", "canvas_fp_007", "canvas_fp_008", "canvas_fp_009", "canvas_fp_010",
            "canvas_fp_011", "canvas_fp_012"
        };

        // WebGL指纹数据池
        private static readonly string[] WebGLFingerprintPool = new[]
        {
            "webgl_fp_001", "webgl_fp_002", "webgl_fp_003", "webgl_fp_004", "webgl_fp_005",
            "webgl_fp_006", "webgl_fp_007", "webgl_fp_008", "webgl_fp_009", "webgl_fp_010",
            "webgl_fp_011", "webgl_fp_012"
        };

        // 音频上下文指纹数据池
        private static readonly string[] AudioContextFingerprintPool = new[]
        {
            "0426959D", "1A2B3C4D", "5E6F7A8B", "9C0D1E2F", "3A4B5C6D",
            "7E8F9A0B", "1C2D3E4F", "5A6B7C8D", "9E0F1A2B", "3C4D5E6F",
            "7A8B9C0D", "1E2F3A4B"
        };

        // 媒体设备指纹数据池
        private static readonly string[] MediaDevicesFingerprintPool = new[]
        {
            "Auto", "Manual_001", "Manual_002", "Manual_003", "Manual_004",
            "Manual_005", "Manual_006", "Manual_007", "Manual_008", "Manual_009",
            "Manual_010", "Manual_011"
        };

        // ClientRects指纹数据池
        private static readonly string[] ClientRectsFingerprintPool = new[]
        {
            "D79834F2", "A1B2C3D4", "E5F6A7B8", "C9D0E1F2", "A3B4C5D6",
            "E7F8A9B0", "C1D2E3F4", "A5B6C7D8", "E9F0A1B2", "C3D4E5F6",
            "A7B8C9D0", "E1F2A3B4"
        };

        // 语音指纹数据池
        private static readonly string[] SpeechVoicesFingerprintPool = new[]
        {
            "noise_001", "noise_002", "noise_003", "noise_004", "noise_005",
            "noise_006", "noise_007", "noise_008", "noise_009", "noise_010",
            "noise_011", "noise_012"
        };

        // GPU渲染器信息数据池
        private static readonly string[] WebGLRendererPool = new[]
        {
            "ANGLE (NVIDIA GeForce RTX 3060 Direct3D11 vs_5_0 ps_5_0)",
            "ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0)",
            "ANGLE (NVIDIA GeForce RTX 3080 Direct3D11 vs_5_0 ps_5_0)",
            "ANGLE (NVIDIA GeForce GTX 1660 Direct3D11 vs_5_0 ps_5_0)",
            "ANGLE (NVIDIA GeForce GTX 1070 Direct3D11 vs_5_0 ps_5_0)",
            "ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0)",
            "ANGLE (AMD Radeon RX 6700 XT Direct3D11 vs_5_0 ps_5_0)",
            "ANGLE (Intel(R) UHD Graphics 630 Direct3D11 vs_5_0 ps_5_0)",
            "ANGLE (Intel(R) Iris(R) Xe Graphics Direct3D11 vs_5_0 ps_5_0)",
            "ANGLE (NVIDIA GeForce RTX 4060 Direct3D11 vs_5_0 ps_5_0)",
            "ANGLE (NVIDIA GeForce RTX 4070 Direct3D11 vs_5_0 ps_5_0)",
            "ANGLE (AMD Radeon RX 7600 Direct3D11 vs_5_0 ps_5_0)"
        };

        // GPU厂商信息数据池
        private static readonly string[] WebGLVendorPool = new[]
        {
            "Google Inc. (NVIDIA)", "Google Inc. (AMD)", "Google Inc. (Intel)",
            "Google Inc. (NVIDIA Corporation)", "Google Inc. (Advanced Micro Devices, Inc.)",
            "Google Inc. (Intel Corporation)", "Google Inc. (NVIDIA Corp.)",
            "Google Inc. (AMD Inc.)", "Google Inc. (Intel Corp.)",
            "Google Inc. (NVIDIA Technologies)", "Google Inc. (AMD Technologies)",
            "Google Inc. (Intel Technologies)"
        };

        // CPU核心数数据池
        private static readonly int[] CPUCoresPool = new[]
        {
            4, 6, 8, 12, 16, 20, 24, 32, 2, 10, 14, 18
        };

        // RAM大小数据池
        private static readonly string[] RAMSizePool = new[]
        {
            "4 GB", "8 GB", "16 GB", "32 GB", "12 GB", "24 GB", "6 GB", "20 GB", "48 GB", "64 GB", "10 GB", "14 GB"
        };

        // 设备名称数据池
        private static readonly string[] DeviceNamePool = new[]
        {
            "DESKTOP-87EL3RX", "DESKTOP-A1B2C3D", "DESKTOP-E4F5G6H", "DESKTOP-I7J8K9L",
            "DESKTOP-M0N1O2P", "DESKTOP-Q3R4S5T", "DESKTOP-U6V7W8X", "DESKTOP-Y9Z0A1B",
            "DESKTOP-C2D3E4F", "DESKTOP-G5H6I7J", "DESKTOP-K8L9M0N", "DESKTOP-O1P2Q3R"
        };

        // MAC地址数据池
        private static readonly string[] MACAddressPool = new[]
        {
            "E4-42-A6-5D-13-98", "A1-B2-C3-D4-E5-F6", "12-34-56-78-9A-BC",
            "DE-F0-12-34-56-78", "9A-BC-DE-F0-12-34", "56-78-9A-BC-DE-F0",
            "34-56-78-9A-BC-DE", "F0-12-34-56-78-9A", "BC-DE-F0-12-34-56",
            "78-9A-BC-DE-F0-12", "AA-BB-CC-DD-EE-FF", "11-22-33-44-55-66"
        };

        // Do Not Track设置数据池
        private static readonly string[] DoNotTrackPool = new[]
        {
            "unspecified", "1", "0", "null", "undefined", "default",
            "enabled", "disabled", "auto", "manual", "system", "user"
        };

        // 硬件加速设置数据池
        private static readonly string[] HardwareAccelerationPool = new[]
        {
            "default", "enabled", "disabled", "auto", "force", "software",
            "hardware", "system", "user", "manual", "automatic", "optimized"
        };

        // TLS特性设置数据池
        private static readonly string[] TLSFeaturesPool = new[]
        {
            "default", "disabled", "enabled", "auto", "manual", "system",
            "secure", "standard", "enhanced", "basic", "advanced", "custom"
        };

        /// <summary>
        /// 生成完整的浏览器指纹
        /// </summary>
        public static void GenerateAdvancedFingerprint(BrowserFingerprint fingerprint)
        {
            fingerprint.CanvasFingerprint = CanvasFingerprintPool[_random.Next(CanvasFingerprintPool.Length)];
            fingerprint.WebGLFingerprint = WebGLFingerprintPool[_random.Next(WebGLFingerprintPool.Length)];
            fingerprint.AudioContextFingerprint = AudioContextFingerprintPool[_random.Next(AudioContextFingerprintPool.Length)];
            fingerprint.MediaDevicesFingerprint = MediaDevicesFingerprintPool[_random.Next(MediaDevicesFingerprintPool.Length)];
            fingerprint.ClientRectsFingerprint = ClientRectsFingerprintPool[_random.Next(ClientRectsFingerprintPool.Length)];
            fingerprint.SpeechVoicesFingerprint = SpeechVoicesFingerprintPool[_random.Next(SpeechVoicesFingerprintPool.Length)];
            fingerprint.WebGLRenderer = WebGLRendererPool[_random.Next(WebGLRendererPool.Length)];
            fingerprint.WebGLVendor = WebGLVendorPool[_random.Next(WebGLVendorPool.Length)];
            fingerprint.CPUCores = CPUCoresPool[_random.Next(CPUCoresPool.Length)];
            fingerprint.RAMSize = RAMSizePool[_random.Next(RAMSizePool.Length)];
            fingerprint.DeviceName = DeviceNamePool[_random.Next(DeviceNamePool.Length)];
            fingerprint.MACAddress = MACAddressPool[_random.Next(MACAddressPool.Length)];
            fingerprint.DoNotTrack = DoNotTrackPool[_random.Next(DoNotTrackPool.Length)];
            fingerprint.PortScanProtection = _random.Next(2) == 1;
            fingerprint.HardwareAcceleration = HardwareAccelerationPool[_random.Next(HardwareAccelerationPool.Length)];
            fingerprint.TLSFeatures = TLSFeaturesPool[_random.Next(TLSFeaturesPool.Length)];
        }

        /// <summary>
        /// 生成JavaScript指纹注入脚本
        /// </summary>
        public static string GenerateFingerprintScript(BrowserFingerprint fingerprint)
        {
            var script = new StringBuilder();
            
            script.AppendLine("(function() {");
            script.AppendLine("  'use strict';");
            script.AppendLine();
            
            // Canvas指纹伪装
            script.AppendLine("  // Canvas指纹伪装");
            script.AppendLine("  const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;");
            script.AppendLine("  const originalGetImageData = CanvasRenderingContext2D.prototype.getImageData;");
            script.AppendLine("  HTMLCanvasElement.prototype.toDataURL = function(...args) {");
            script.AppendLine("    const result = originalToDataURL.apply(this, args);");
            script.AppendLine($"    // 添加微小的随机变化来模拟真实的Canvas指纹");
            script.AppendLine($"    const hash = '{fingerprint.CanvasFingerprint}';");
            script.AppendLine("    return result.slice(0, -10) + hash.slice(0, 8) + result.slice(-2);");
            script.AppendLine("  };");
            script.AppendLine("  CanvasRenderingContext2D.prototype.getImageData = function(...args) {");
            script.AppendLine("    const result = originalGetImageData.apply(this, args);");
            script.AppendLine($"    // 对ImageData添加微小噪音");
            script.AppendLine($"    const noise = parseInt('{fingerprint.CanvasFingerprint}'.slice(-2), 16) % 3;");
            script.AppendLine("    if (result.data.length > 10) {");
            script.AppendLine("      for (let i = 0; i < Math.min(10, result.data.length); i += 4) {");
            script.AppendLine("        result.data[i] = Math.min(255, result.data[i] + noise);");
            script.AppendLine("      }");
            script.AppendLine("    }");
            script.AppendLine("    return result;");
            script.AppendLine("  };");
            script.AppendLine();

            // WebGL指纹伪装
            script.AppendLine("  // WebGL指纹伪装");
            script.AppendLine("  const originalGetParameter = WebGLRenderingContext.prototype.getParameter;");
            script.AppendLine("  WebGLRenderingContext.prototype.getParameter = function(parameter) {");
            script.AppendLine("    if (parameter === this.RENDERER) {");
            script.AppendLine($"      return '{fingerprint.WebGLRenderer}';");
            script.AppendLine("    }");
            script.AppendLine("    if (parameter === this.VENDOR) {");
            script.AppendLine($"      return '{fingerprint.WebGLVendor}';");
            script.AppendLine("    }");
            script.AppendLine("    return originalGetParameter.apply(this, arguments);");
            script.AppendLine("  };");
            script.AppendLine();

            // WebGL2指纹伪装
            script.AppendLine("  // WebGL2指纹伪装");
            script.AppendLine("  if (window.WebGL2RenderingContext) {");
            script.AppendLine("    const originalGetParameter2 = WebGL2RenderingContext.prototype.getParameter;");
            script.AppendLine("    WebGL2RenderingContext.prototype.getParameter = function(parameter) {");
            script.AppendLine("      if (parameter === this.RENDERER) {");
            script.AppendLine($"        return '{fingerprint.WebGLRenderer}';");
            script.AppendLine("      }");
            script.AppendLine("      if (parameter === this.VENDOR) {");
            script.AppendLine($"        return '{fingerprint.WebGLVendor}';");
            script.AppendLine("      }");
            script.AppendLine("      return originalGetParameter2.apply(this, arguments);");
            script.AppendLine("    };");
            script.AppendLine("  }");
            script.AppendLine();

            // WebGPU配置伪装
            script.AppendLine("  // WebGPU配置伪装");
            script.AppendLine("  if (navigator.gpu) {");
            script.AppendLine("    const originalRequestAdapter = navigator.gpu.requestAdapter;");
            script.AppendLine("    navigator.gpu.requestAdapter = function(options) {");
            script.AppendLine("      return originalRequestAdapter.apply(this, arguments).then(adapter => {");
            script.AppendLine("        if (adapter) {");
            script.AppendLine("          // 基于WebGL的WebGPU配置");
            script.AppendLine($"          Object.defineProperty(adapter, 'name', {{ value: 'WebGPU-{fingerprint.WebGLRenderer.Split(' ')[1] ?? "Generic"}', writable: false }});");
            script.AppendLine("        }");
            script.AppendLine("        return adapter;");
            script.AppendLine("      });");
            script.AppendLine("    };");
            script.AppendLine("  }");
            script.AppendLine();

            // AudioContext指纹伪装
            script.AppendLine("  // AudioContext指纹伪装");
            script.AppendLine("  const originalCreateAnalyser = AudioContext.prototype.createAnalyser;");
            script.AppendLine("  const originalCreateOscillator = AudioContext.prototype.createOscillator;");
            script.AppendLine("  const originalCreateDynamicsCompressor = AudioContext.prototype.createDynamicsCompressor;");
            script.AppendLine("  AudioContext.prototype.createAnalyser = function() {");
            script.AppendLine("    const analyser = originalCreateAnalyser.apply(this, arguments);");
            script.AppendLine($"    analyser._fingerprintId = '{fingerprint.AudioContextFingerprint}';");
            script.AppendLine("    return analyser;");
            script.AppendLine("  };");
            script.AppendLine("  AudioContext.prototype.createOscillator = function() {");
            script.AppendLine("    const oscillator = originalCreateOscillator.apply(this, arguments);");
            script.AppendLine($"    const baseFreq = oscillator.frequency.value;");
            script.AppendLine($"    const noise = parseInt('{fingerprint.AudioContextFingerprint}'.slice(-2), 16) / 1000;");
            script.AppendLine("    oscillator.frequency.value = baseFreq + noise;");
            script.AppendLine("    return oscillator;");
            script.AppendLine("  };");
            script.AppendLine("  AudioContext.prototype.createDynamicsCompressor = function() {");
            script.AppendLine("    const compressor = originalCreateDynamicsCompressor.apply(this, arguments);");
            script.AppendLine($"    const noise = parseInt('{fingerprint.AudioContextFingerprint}'.slice(0, 2), 16) / 10000;");
            script.AppendLine("    compressor.threshold.value = compressor.threshold.value + noise;");
            script.AppendLine("    return compressor;");
            script.AppendLine("  };");
            script.AppendLine();

            // 媒体设备指纹伪装
            script.AppendLine("  // 媒体设备指纹伪装");
            script.AppendLine("  if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {");
            script.AppendLine("    const originalEnumerateDevices = navigator.mediaDevices.enumerateDevices;");
            script.AppendLine("    navigator.mediaDevices.enumerateDevices = function() {");
            script.AppendLine("      return originalEnumerateDevices.apply(this, arguments).then(devices => {");
            script.AppendLine($"        // 修改设备标识符");
            script.AppendLine($"        const deviceHash = '{fingerprint.MediaDevicesFingerprint}';");
            script.AppendLine("        devices.forEach((device, index) => {");
            script.AppendLine("          if (device.deviceId && device.deviceId !== 'default') {");
            script.AppendLine("            const newId = deviceHash + '_' + index + '_' + device.kind;");
            script.AppendLine("            Object.defineProperty(device, 'deviceId', { value: newId, writable: false });");
            script.AppendLine("          }");
            script.AppendLine("          if (device.groupId) {");
            script.AppendLine("            const newGroupId = deviceHash + '_group_' + index;");
            script.AppendLine("            Object.defineProperty(device, 'groupId', { value: newGroupId, writable: false });");
            script.AppendLine("          }");
            script.AppendLine("        });");
            script.AppendLine("        return devices;");
            script.AppendLine("      });");
            script.AppendLine("    };");
            script.AppendLine("  }");
            script.AppendLine();

            // 权限API伪装
            script.AppendLine("  // 权限API伪装");
            script.AppendLine("  if (navigator.permissions && navigator.permissions.query) {");
            script.AppendLine("    const originalQuery = navigator.permissions.query;");
            script.AppendLine("    navigator.permissions.query = function(permissionDesc) {");
            script.AppendLine("      return originalQuery.apply(this, arguments).then(result => {");
            script.AppendLine($"        // 根据指纹随机化权限状态");
            script.AppendLine($"        const hash = '{fingerprint.MediaDevicesFingerprint}';");
            script.AppendLine("        const shouldModify = parseInt(hash.slice(-1), 16) % 3 === 0;");
            script.AppendLine("        if (shouldModify && permissionDesc.name === 'camera') {");
            script.AppendLine("          Object.defineProperty(result, 'state', { value: 'prompt', writable: false });");
            script.AppendLine("        }");
            script.AppendLine("        return result;");
            script.AppendLine("      });");
            script.AppendLine("    };");
            script.AppendLine("  }");
            script.AppendLine();

            // ClientRects指纹伪装
            script.AppendLine("  // ClientRects指纹伪装");
            script.AppendLine("  const originalGetClientRects = Element.prototype.getClientRects;");
            script.AppendLine("  Element.prototype.getClientRects = function() {");
            script.AppendLine("    const rects = originalGetClientRects.apply(this, arguments);");
            script.AppendLine($"    rects._fingerprintId = '{fingerprint.ClientRectsFingerprint}';");
            script.AppendLine("    return rects;");
            script.AppendLine("  };");
            script.AppendLine();

            // SpeechVoices指纹伪装
            script.AppendLine("  // SpeechVoices指纹伪装");
            script.AppendLine("  if (window.speechSynthesis) {");
            script.AppendLine("    const originalGetVoices = speechSynthesis.getVoices;");
            script.AppendLine("    speechSynthesis.getVoices = function() {");
            script.AppendLine("      const voices = originalGetVoices.apply(this, arguments);");
            script.AppendLine($"      voices._fingerprintId = '{fingerprint.SpeechVoicesFingerprint}';");
            script.AppendLine("      return voices;");
            script.AppendLine("    };");
            script.AppendLine("  }");
            script.AppendLine();

            // CPU核心数伪装
            script.AppendLine("  // CPU核心数伪装");
            script.AppendLine("  Object.defineProperty(navigator, 'hardwareConcurrency', {");
            script.AppendLine($"    value: {fingerprint.CPUCores},");
            script.AppendLine("    writable: false,");
            script.AppendLine("    enumerable: true,");
            script.AppendLine("    configurable: false");
            script.AppendLine("  });");
            script.AppendLine();

            // RAM大小伪装（通过deviceMemory）
            script.AppendLine("  // RAM大小伪装");
            var ramGB = fingerprint.RAMSize.Replace(" GB", "");
            if (int.TryParse(ramGB, out int ramValue))
            {
                script.AppendLine("  Object.defineProperty(navigator, 'deviceMemory', {");
                script.AppendLine($"    value: {ramValue},");
                script.AppendLine("    writable: false,");
                script.AppendLine("    enumerable: true,");
                script.AppendLine("    configurable: false");
                script.AppendLine("  });");
            }
            script.AppendLine();

            // Do Not Track设置伪装
            script.AppendLine("  // Do Not Track设置伪装");
            script.AppendLine("  Object.defineProperty(navigator, 'doNotTrack', {");
            script.AppendLine($"    value: '{fingerprint.DoNotTrack}',");
            script.AppendLine("    writable: false,");
            script.AppendLine("    enumerable: true,");
            script.AppendLine("    configurable: false");
            script.AppendLine("  });");
            script.AppendLine();

            // 设备名称伪装（通过platform相关属性）
            script.AppendLine("  // 设备名称伪装");
            script.AppendLine("  Object.defineProperty(navigator, 'platform', {");
            script.AppendLine("    value: 'Win32',");
            script.AppendLine("    writable: false,");
            script.AppendLine("    enumerable: true,");
            script.AppendLine("    configurable: false");
            script.AppendLine("  });");
            script.AppendLine();

            // MAC地址伪装（通过网络接口）
            script.AppendLine("  // MAC地址伪装");
            script.AppendLine("  if (navigator.connection) {");
            script.AppendLine("    Object.defineProperty(navigator.connection, 'mac', {");
            script.AppendLine($"      value: '{fingerprint.MACAddress}',");
            script.AppendLine("      writable: false,");
            script.AppendLine("      enumerable: true,");
            script.AppendLine("      configurable: false");
            script.AppendLine("    });");
            script.AppendLine("  }");
            script.AppendLine();

            // 设备名称通过其他方式伪装
            script.AppendLine("  // 设备名称通过hostname伪装");
            script.AppendLine("  if (window.location && window.location.hostname) {");
            script.AppendLine("    try {");
            script.AppendLine($"      window._deviceName = '{fingerprint.DeviceName}';");
            script.AppendLine("    } catch (e) {}");
            script.AppendLine("  }");
            script.AppendLine();

            // 网络连接信息伪装
            script.AppendLine("  // 网络连接信息伪装");
            script.AppendLine("  if (navigator.connection) {");
            script.AppendLine("    Object.defineProperty(navigator.connection, 'effectiveType', {");
            script.AppendLine("      value: '4g',");
            script.AppendLine("      writable: false,");
            script.AppendLine("      enumerable: true,");
            script.AppendLine("      configurable: false");
            script.AppendLine("    });");
            script.AppendLine("  }");
            script.AppendLine();

            // 屏幕信息伪装
            script.AppendLine("  // 屏幕信息伪装");
            var screenWidth = 1920 + (_random.Next(-200, 201)); // 1720-2120
            var screenHeight = 1080 + (_random.Next(-100, 101)); // 980-1180
            script.AppendLine("  Object.defineProperty(screen, 'width', {");
            script.AppendLine($"    value: {screenWidth},");
            script.AppendLine("    writable: false,");
            script.AppendLine("    enumerable: true,");
            script.AppendLine("    configurable: false");
            script.AppendLine("  });");
            script.AppendLine("  Object.defineProperty(screen, 'height', {");
            script.AppendLine($"    value: {screenHeight},");
            script.AppendLine("    writable: false,");
            script.AppendLine("    enumerable: true,");
            script.AppendLine("    configurable: false");
            script.AppendLine("  });");
            script.AppendLine("  Object.defineProperty(screen, 'availWidth', {");
            script.AppendLine($"    value: {screenWidth},");
            script.AppendLine("    writable: false,");
            script.AppendLine("    enumerable: true,");
            script.AppendLine("    configurable: false");
            script.AppendLine("  });");
            script.AppendLine("  Object.defineProperty(screen, 'availHeight', {");
            script.AppendLine($"    value: {screenHeight - 40},"); // 减去任务栏高度
            script.AppendLine("    writable: false,");
            script.AppendLine("    enumerable: true,");
            script.AppendLine("    configurable: false");
            script.AppendLine("  });");
            script.AppendLine();

            // 时区信息伪装
            script.AppendLine("  // 时区信息伪装");
            script.AppendLine("  const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;");
            script.AppendLine("  Date.prototype.getTimezoneOffset = function() {");
            var timezoneOffset = GetTimezoneOffset(fingerprint.TimeZone);
            script.AppendLine($"    return {timezoneOffset};");
            script.AppendLine("  };");
            script.AppendLine();

            // 语言信息伪装
            script.AppendLine("  // 语言信息伪装");
            script.AppendLine("  Object.defineProperty(navigator, 'language', {");
            script.AppendLine($"    value: '{fingerprint.Language.Split(',')[0]}',");
            script.AppendLine("    writable: false,");
            script.AppendLine("    enumerable: true,");
            script.AppendLine("    configurable: false");
            script.AppendLine("  });");
            script.AppendLine("  Object.defineProperty(navigator, 'languages', {");
            script.AppendLine($"    value: ['{fingerprint.Language.Split(',')[0]}', 'en-US'],");
            script.AppendLine("    writable: false,");
            script.AppendLine("    enumerable: true,");
            script.AppendLine("    configurable: false");
            script.AppendLine("  });");
            script.AppendLine();

            // 性能API伪装
            script.AppendLine("  // 性能API伪装");
            script.AppendLine("  if (window.performance && window.performance.now) {");
            script.AppendLine("    const originalNow = performance.now;");
            script.AppendLine($"    const timeOffset = parseInt('{fingerprint.DeviceName}'.slice(-2), 16) / 100;");
            script.AppendLine("    performance.now = function() {");
            script.AppendLine("      return originalNow.apply(this, arguments) + timeOffset;");
            script.AppendLine("    };");
            script.AppendLine("  }");
            script.AppendLine();

            // 电池API伪装
            script.AppendLine("  // 电池API伪装");
            script.AppendLine("  if (navigator.getBattery) {");
            script.AppendLine("    const originalGetBattery = navigator.getBattery;");
            script.AppendLine("    navigator.getBattery = function() {");
            script.AppendLine("      return originalGetBattery.apply(this, arguments).then(battery => {");
            script.AppendLine($"        const batteryLevel = 0.{_random.Next(20, 95)};");
            script.AppendLine($"        const isCharging = {(_random.Next(2) == 1 ? "true" : "false")};");
            script.AppendLine("        Object.defineProperty(battery, 'level', { value: batteryLevel, writable: false });");
            script.AppendLine("        Object.defineProperty(battery, 'charging', { value: isCharging, writable: false });");
            script.AppendLine("        return battery;");
            script.AppendLine("      });");
            script.AppendLine("    };");
            script.AppendLine("  }");
            script.AppendLine();

            // 插件信息伪装
            script.AppendLine("  // 插件信息伪装");
            script.AppendLine("  Object.defineProperty(navigator, 'plugins', {");
            script.AppendLine("    value: {");
            script.AppendLine("      length: 0,");
            script.AppendLine("      item: function() { return null; },");
            script.AppendLine("      namedItem: function() { return null; },");
            script.AppendLine("      refresh: function() {}");
            script.AppendLine("    },");
            script.AppendLine("    writable: false,");
            script.AppendLine("    enumerable: true,");
            script.AppendLine("    configurable: false");
            script.AppendLine("  });");
            script.AppendLine();

            // MIME类型伪装
            script.AppendLine("  // MIME类型伪装");
            script.AppendLine("  Object.defineProperty(navigator, 'mimeTypes', {");
            script.AppendLine("    value: {");
            script.AppendLine("      length: 0,");
            script.AppendLine("      item: function() { return null; },");
            script.AppendLine("      namedItem: function() { return null; }");
            script.AppendLine("    },");
            script.AppendLine("    writable: false,");
            script.AppendLine("    enumerable: true,");
            script.AppendLine("    configurable: false");
            script.AppendLine("  });");
            script.AppendLine();

            // 端口扫描保护伪装
            script.AppendLine("  // 端口扫描保护伪装");
            script.AppendLine("  const originalFetch = window.fetch;");
            script.AppendLine("  window.fetch = function(url, options) {");
            script.AppendLine($"    const portScanProtection = {fingerprint.PortScanProtection.ToString().ToLower()};");
            script.AppendLine("    if (portScanProtection && typeof url === 'string') {");
            script.AppendLine("      const urlObj = new URL(url, window.location.href);");
            script.AppendLine("      const port = urlObj.port;");
            script.AppendLine("      if (port && ['22', '23', '25', '53', '80', '110', '143', '993', '995'].includes(port)) {");
            script.AppendLine("        return Promise.reject(new Error('Port scan protection enabled'));");
            script.AppendLine("      }");
            script.AppendLine("    }");
            script.AppendLine("    return originalFetch.apply(this, arguments);");
            script.AppendLine("  };");
            script.AppendLine();

            // TLS特性伪装
            script.AppendLine("  // TLS特性伪装");
            script.AppendLine("  if (window.crypto && window.crypto.subtle) {");
            script.AppendLine("    const originalGenerateKey = window.crypto.subtle.generateKey;");
            script.AppendLine("    window.crypto.subtle.generateKey = function(algorithm, extractable, keyUsages) {");
            script.AppendLine($"      const tlsFeatures = '{fingerprint.TLSFeatures}';");
            script.AppendLine("      if (tlsFeatures === 'disabled' && algorithm.name === 'RSA-PSS') {");
            script.AppendLine("        return Promise.reject(new Error('TLS feature disabled'));");
            script.AppendLine("      }");
            script.AppendLine("      return originalGenerateKey.apply(this, arguments);");
            script.AppendLine("    };");
            script.AppendLine("  }");
            script.AppendLine();

            // 硬件加速伪装
            script.AppendLine("  // 硬件加速伪装");
            script.AppendLine("  if (window.OffscreenCanvas) {");
            script.AppendLine("    const originalGetContext = OffscreenCanvas.prototype.getContext;");
            script.AppendLine("    OffscreenCanvas.prototype.getContext = function(contextType, contextAttributes) {");
            script.AppendLine($"      const hwAcceleration = '{fingerprint.HardwareAcceleration}';");
            script.AppendLine("      if (hwAcceleration === 'disabled' && (contextType === 'webgl' || contextType === 'webgl2')) {");
            script.AppendLine("        return null;");
            script.AppendLine("      }");
            script.AppendLine("      return originalGetContext.apply(this, arguments);");
            script.AppendLine("    };");
            script.AppendLine("  }");
            script.AppendLine();

            script.AppendLine("})();");

            return script.ToString();
        }

        /// <summary>
        /// 根据时区获取时区偏移量（分钟）
        /// </summary>
        /// <summary>
        /// 根据时区获取时区偏移量（分钟）
        /// 注意：JavaScript中getTimezoneOffset()返回的是本地时间与UTC的分钟差
        /// 负数表示本地时间比UTC早（东时区），正数表示本地时间比UTC晚（西时区）
        /// </summary>
        private static int GetTimezoneOffset(string timeZone)
        {
            return timeZone switch
            {
                // 亚洲时区
                "Asia/Shanghai" => -480,      // UTC+8
                "Asia/Tokyo" => -540,         // UTC+9
                "Asia/Seoul" => -540,         // UTC+9
                "Asia/Baku" => -240,          // UTC+4 (阿塞拜疆)
                "Asia/Dubai" => -240,         // UTC+4
                "Asia/Singapore" => -480,     // UTC+8
                "Asia/Bangkok" => -420,       // UTC+7
                "Asia/Jakarta" => -420,       // UTC+7
                "Asia/Manila" => -480,        // UTC+8
                "Asia/Kuala_Lumpur" => -480,  // UTC+8
                "Asia/Hong_Kong" => -480,     // UTC+8
                "Asia/Taipei" => -480,        // UTC+8
                "Asia/Kolkata" => -330,       // UTC+5:30
                "Asia/Karachi" => -300,       // UTC+5
                "Asia/Tashkent" => -300,      // UTC+5
                "Asia/Almaty" => -360,        // UTC+6
                "Asia/Dhaka" => -360,         // UTC+6
                "Asia/Yekaterinburg" => -300, // UTC+5
                "Asia/Novosibirsk" => -420,   // UTC+7
                "Asia/Riyadh" => -180,        // UTC+3
                "Asia/Tehran" => -210,        // UTC+3:30
                "Asia/Jerusalem" => -120,     // UTC+2

                // 欧洲时区
                "Europe/London" => 0,         // UTC+0
                "Europe/Paris" => -60,        // UTC+1
                "Europe/Berlin" => -60,       // UTC+1
                "Europe/Rome" => -60,         // UTC+1
                "Europe/Madrid" => -60,       // UTC+1
                "Europe/Amsterdam" => -60,    // UTC+1
                "Europe/Brussels" => -60,     // UTC+1
                "Europe/Vienna" => -60,       // UTC+1
                "Europe/Prague" => -60,       // UTC+1
                "Europe/Warsaw" => -60,       // UTC+1
                "Europe/Stockholm" => -60,    // UTC+1
                "Europe/Oslo" => -60,         // UTC+1
                "Europe/Copenhagen" => -60,   // UTC+1
                "Europe/Helsinki" => -120,    // UTC+2
                "Europe/Athens" => -120,      // UTC+2
                "Europe/Sofia" => -120,       // UTC+2
                "Europe/Bucharest" => -120,   // UTC+2
                "Europe/Kiev" => -120,        // UTC+2
                "Europe/Moscow" => -180,      // UTC+3
                "Europe/Istanbul" => -180,    // UTC+3

                // 北美时区
                "America/New_York" => 300,    // UTC-5
                "America/Chicago" => 360,     // UTC-6
                "America/Denver" => 420,      // UTC-7
                "America/Los_Angeles" => 480, // UTC-8
                "America/Toronto" => 300,     // UTC-5
                "America/Vancouver" => 480,   // UTC-8
                "America/Montreal" => 300,    // UTC-5
                "America/Mexico_City" => 360, // UTC-6
                "America/Tijuana" => 480,     // UTC-8
                "America/Cancun" => 300,      // UTC-5
                "Canada/Pacific" => 480,      // UTC-8

                // 南美时区
                "America/Sao_Paulo" => 180,   // UTC-3
                "America/Buenos_Aires" => 180, // UTC-3
                "America/Santiago" => 180,    // UTC-3
                "America/Lima" => 300,        // UTC-5
                "America/Bogota" => 300,      // UTC-5
                "America/Caracas" => 240,     // UTC-4

                // 大洋洲时区
                "Australia/Sydney" => -600,   // UTC+10
                "Australia/Melbourne" => -600, // UTC+10
                "Australia/Brisbane" => -600, // UTC+10
                "Australia/Perth" => -480,    // UTC+8
                "Pacific/Auckland" => -720,   // UTC+12

                // 非洲时区
                "Africa/Cairo" => -120,       // UTC+2
                "Africa/Johannesburg" => -120, // UTC+2
                "Africa/Lagos" => -60,        // UTC+1
                "Africa/Casablanca" => 0,     // UTC+0

                _ => -480 // 默认UTC+8 (Asia/Shanghai)
            };
        }
    }
}
