# 浏览器指纹伪装功能说明

## 功能概述

已成功为AWS注册工具添加了完整的浏览器指纹伪装功能，支持单线程和多线程模式，每个窗口使用不同的随机指纹数据。

## 新增的指纹伪装项目

### 1. Canvas指纹伪装
- **功能**: 伪装Canvas绘图指纹
- **实现**: 重写HTMLCanvasElement.prototype.toDataURL和CanvasRenderingContext2D.prototype.getImageData方法
- **特点**: 添加微小随机变化和噪音，模拟真实的Canvas指纹差异
- **随机选项**: 12个不同的Canvas指纹标识

### 2. WebGL图像指纹伪装
- **功能**: 伪装WebGL渲染器和厂商信息
- **实现**: 重写WebGLRenderingContext和WebGL2RenderingContext的getParameter方法
- **特点**: 支持WebGL和WebGL2，提供真实的GPU信息
- **随机选项**: 12个不同的GPU渲染器信息和厂商信息

### 3. AudioContext指纹伪装
- **功能**: 伪装音频上下文指纹
- **实现**: 重写AudioContext的createAnalyser、createOscillator、createDynamicsCompressor方法
- **特点**: 对音频频率和压缩器参数添加微小噪音
- **随机选项**: 12个不同的音频指纹标识（如"0426959D"）

### 4. 媒体设备指纹伪装
- **功能**: 伪装媒体设备枚举和权限
- **实现**: 重写navigator.mediaDevices.enumerateDevices和navigator.permissions.query方法
- **特点**: 修改设备ID和组ID，随机化权限状态
- **随机选项**: 12个不同的设备指纹标识

### 5. ClientRects指纹伪装
- **功能**: 伪装元素客户端矩形信息
- **实现**: 重写Element.prototype.getClientRects方法
- **随机选项**: 12个不同的ClientRects指纹标识

### 6. SpeechVoices指纹伪装
- **功能**: 伪装语音合成声音列表
- **实现**: 重写speechSynthesis.getVoices方法
- **随机选项**: 12个不同的语音指纹标识

### 7. CPU核心数伪装
- **功能**: 伪装处理器核心数量
- **实现**: 重写navigator.hardwareConcurrency属性
- **随机选项**: 12个不同的核心数（2-32核）

### 8. RAM大小伪装
- **功能**: 伪装设备内存大小
- **实现**: 重写navigator.deviceMemory属性
- **随机选项**: 12个不同的内存大小（4GB-64GB）

### 9. 设备名称伪装
- **功能**: 伪装设备名称
- **随机选项**: 12个不同的设备名称（如"DESKTOP-87EL3RX"）

### 10. MAC地址伪装
- **功能**: 伪装网络适配器MAC地址
- **随机选项**: 12个不同的MAC地址

### 11. Do Not Track设置伪装
- **功能**: 伪装DNT隐私设置
- **实现**: 重写navigator.doNotTrack属性
- **随机选项**: 12个不同的DNT设置值

### 12. 端口扫描保护
- **功能**: 随机启用/禁用端口扫描保护
- **随机选项**: 启用或禁用

### 13. 硬件加速设置
- **功能**: 伪装硬件加速配置
- **随机选项**: 12个不同的硬件加速设置

### 14. TLS特性设置
- **功能**: 伪装TLS安全特性配置
- **随机选项**: 12个不同的TLS特性设置

### 15. 屏幕信息伪装
- **功能**: 伪装屏幕分辨率和可用区域
- **实现**: 重写screen对象的width、height、availWidth、availHeight属性
- **特点**: 基于1920x1080添加随机变化（±200x±100像素）

### 16. 时区信息伪装
- **功能**: 伪装时区偏移量
- **实现**: 重写Date.prototype.getTimezoneOffset方法
- **特点**: 根据设置的时区返回正确的偏移量

### 17. 语言信息增强
- **功能**: 完整的语言环境伪装
- **实现**: 重写navigator.language和navigator.languages属性
- **特点**: 与时区和地理位置保持一致

### 18. 性能API伪装
- **功能**: 伪装性能计时器
- **实现**: 重写performance.now方法
- **特点**: 添加微小的时间偏移

### 19. 电池API伪装
- **功能**: 伪装电池状态信息
- **实现**: 重写navigator.getBattery方法
- **特点**: 随机化电池电量和充电状态

### 20. 插件和MIME类型伪装
- **功能**: 隐藏浏览器插件信息
- **实现**: 重写navigator.plugins和navigator.mimeTypes属性
- **特点**: 返回空的插件列表，增强隐私保护

### 21. 网络连接信息伪装
- **功能**: 伪装网络连接类型
- **实现**: 重写navigator.connection.effectiveType属性
- **特点**: 统一显示为4G连接

## 技术实现

### 核心文件
1. **Models/MultiThreadModels.cs** - 扩展了BrowserFingerprint类
2. **Models/RegistrationData.cs** - 添加了AWSAutoRegister.Models版本的BrowserFingerprint类
3. **Services/BrowserFingerprintService.cs** - 指纹生成和JavaScript注入服务
4. **Services/BrowserFingerprintAdapter.cs** - 命名空间适配器
5. **Services/AutomationService.cs** - 集成指纹注入功能
6. **Services/MultiThreadManager.cs** - 多线程指纹生成

### 实现原理
1. **指纹生成**: 在浏览器启动前为每个线程生成唯一的指纹数据
2. **JavaScript注入**: 使用Playwright的AddInitScriptAsync在页面加载前注入伪装脚本
3. **API重写**: 通过重写浏览器原生API来返回伪装的指纹数据
4. **随机化**: 每个指纹属性都有10+个随机选项，确保多样性

### 兼容性
- ✅ 支持单线程模式
- ✅ 支持多线程模式
- ✅ 每个窗口使用不同的指纹数据
- ✅ 不影响现有功能和网络配置
- ✅ 在浏览器启动前设置指纹

## 使用方式

### 自动启用
指纹伪装功能已自动集成到现有的浏览器启动流程中，无需额外配置。

### 多线程模式
每个线程会自动生成独特的指纹，确保不同窗口有不同的浏览器特征。

### 单线程模式
单线程模式也会生成随机指纹，提供基本的指纹保护。

## 日志记录

系统会记录指纹生成和注入的详细信息：
- 指纹生成日志：记录Canvas、WebGL、CPU等关键指纹信息
- 注入成功日志：确认JavaScript脚本成功注入
- 测试结果日志：验证指纹伪装是否生效

## 验证方法

可以通过以下方式验证指纹伪装是否生效：
1. 查看浏览器启动日志中的指纹信息
2. 使用在线指纹检测网站测试
3. 检查浏览器控制台中的navigator属性值
4. 自动测试功能会在启动时验证以下项目：
   - CPU核心数检测
   - 设备内存检测
   - Do Not Track设置检测
   - WebGL渲染器信息检测
   - Canvas指纹检测
   - 屏幕信息检测
   - 语言信息检测
   - 时区偏移检测（已修复，现在根据国家代码正确计算）
   - 设备信息检测（设备名称、MAC地址、平台信息）
   - 端口扫描保护测试

## 最新更新 (2025-08-02)

### ✅ 修复的问题
1. **时区偏移计算** - 现在支持80+个国家/地区的正确时区偏移
2. **设备内存检测** - 移除了条件检查，确保deviceMemory属性始终被设置
3. **WebGPU配置** - 添加了基于WebGL的WebGPU适配器伪装
4. **MAC地址伪装** - 通过navigator.connection.mac属性伪装
5. **端口扫描保护** - 实现了真实的端口扫描拦截功能
6. **TLS特性伪装** - 添加了加密算法的条件禁用
7. **硬件加速伪装** - 实现了WebGL上下文的条件禁用

### ✅ 新增功能
- 支持80+个时区的精确偏移计算
- 增强的设备信息伪装
- 更完善的指纹验证测试
- 真实的端口扫描保护模拟

## 注意事项

1. 指纹伪装在页面加载前就已生效
2. 每次启动浏览器都会生成新的随机指纹
3. 多线程模式下每个窗口的指纹都不相同
4. 不会影响AWS注册流程的正常功能
