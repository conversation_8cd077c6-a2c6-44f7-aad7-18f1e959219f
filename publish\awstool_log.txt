2025-08-02 17:23:06 [信息] 已选择文件: C:\Users\<USER>\Desktop\test.txt
2025-08-02 17:23:06 [按钮操作] 加载信息 -> 加载数据文件: C:\Users\<USER>\Desktop\test.txt
2025-08-02 17:23:06 [信息] 成功加载 2 条数据
2025-08-02 17:23:08 [信息] 线程数量已选择: 2
2025-08-02 17:23:10 [按钮操作] 开始注册 -> 启动注册流程
2025-08-02 17:23:10 [信息] 开始启动多线程注册，线程数量: 2
2025-08-02 17:23:10 [信息] 开始启动多线程注册，线程数量: 2，数据条数: 2
2025-08-02 17:23:10 [信息] 所有线程已停止并清理
2025-08-02 17:23:10 [信息] 正在初始化多线程服务...
2025-08-02 17:23:10 [信息] 千川手机API服务已初始化
2025-08-02 17:23:10 [信息] 手机号码管理器已初始化，服务商: Qianchuan，将在第一个线程完成第二页后获取手机号码
2025-08-02 17:23:10 [信息] 多线程服务初始化完成
2025-08-02 17:23:10 [信息] 数据分配完成：共2条数据分配给2个线程
2025-08-02 17:23:10 [信息] 线程1分配到1条数据
2025-08-02 17:23:10 [信息] 线程2分配到1条数据
2025-08-02 17:23:10 [信息] 屏幕工作区域: 1280x672
2025-08-02 17:23:10 [信息] 线程1窗口布局: 位置(0, 0), 大小(384x310), 列1行1, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-02 17:23:10 [信息] 线程1获取到数据: Email=<EMAIL>, CountryCode=AZ
2025-08-02 17:23:10 [信息] 为国家代码 AZ 生成智能指纹: 时区=Asia/Baku, 语言=az-AZ,az;q=0.9,en;q=0.8
2025-08-02 17:23:10 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_007, WebGL=ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0), CPU=20核, RAM=48 GB
2025-08-02 17:23:10 线程1：[信息] 已创建，窗口位置: (0, 0)
2025-08-02 17:23:10 线程1：[信息] 添加数据到队列: <EMAIL>
2025-08-02 17:23:10 [信息] 线程1已创建，窗口位置: (0, 0)，指纹: 国家=AZ, 时区=Asia/Baku
2025-08-02 17:23:10 [信息] 屏幕工作区域: 1280x672
2025-08-02 17:23:10 [信息] 线程2窗口布局: 位置(0, 329), 大小(384x310), 列1行2, 宽度30%, 当前列窗口数:2, 间隙19px, 双列模式:False, 底部安全边距:33px, 可用高度:639px
2025-08-02 17:23:10 [信息] 线程2获取到数据: Email=<EMAIL>, CountryCode=AZ
2025-08-02 17:23:10 [信息] 为国家代码 AZ 生成智能指纹: 时区=Asia/Baku, 语言=az-AZ,az;q=0.9,en;q=0.8
2025-08-02 17:23:10 [信息] 已生成高级浏览器指纹: Canvas=canvas_fp_002, WebGL=ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0), CPU=6核, RAM=4 GB
2025-08-02 17:23:10 线程2：[信息] 已创建，窗口位置: (0, 329)
2025-08-02 17:23:10 线程2：[信息] 添加数据到队列: <EMAIL>
2025-08-02 17:23:10 [信息] 线程2已创建，窗口位置: (0, 329)，指纹: 国家=AZ, 时区=Asia/Baku
2025-08-02 17:23:10 [信息] 多线程注册启动成功，共2个线程
2025-08-02 17:23:10 线程1：[信息] 开始启动注册流程
2025-08-02 17:23:10 线程2：[信息] 开始启动注册流程
2025-08-02 17:23:10 线程1：[信息] 开始启动浏览器: 位置(0, 0), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-02 17:23:10 线程2：[信息] 开始启动浏览器: 位置(0, 329), UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36
2025-08-02 17:23:10 线程2：[信息] 启动无痕Chrome浏览器...
2025-08-02 17:23:10 线程1：[信息] 启动无痕Chrome浏览器...
2025-08-02 17:23:10 线程2：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-02 17:23:10 线程1：[信息] [信息] 正在检测Chrome浏览器(无痕模式)... (进度: 0%)
2025-08-02 17:23:10 [信息] 多线程管理窗口已初始化
2025-08-02 17:23:10 [信息] UniformGrid列数已更新为: 1
2025-08-02 17:23:10 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 0, 列数: 1
2025-08-02 17:23:10 [信息] 多线程管理窗口已打开
2025-08-02 17:23:10 [信息] 多线程注册启动成功，共2个线程
2025-08-02 17:23:13 [信息] UniformGrid列数已更新为: 1
2025-08-02 17:23:13 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 1, 列数: 1
2025-08-02 17:23:13 线程1：[信息] [信息] 多线程模式根据指纹国家代码 AZ 设置浏览器语言: Azərbaycan dili (进度: 0%)
2025-08-02 17:23:13 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=AZ, 语言=Azərbaycan dili, 参数=--lang=az-AZ
2025-08-02 17:23:13 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36
2025-08-02 17:23:13 线程1：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-02 17:23:13 [信息] UniformGrid列数已更新为: 1
2025-08-02 17:23:13 [信息] 窗口已设置固定尺寸: 650x608（与主窗口一致）, 最右边位置: (610, 50), 线程数量: 2, 列数: 1
2025-08-02 17:23:13 线程2：[信息] [信息] 多线程模式根据指纹国家代码 AZ 设置浏览器语言: Azərbaycan dili (进度: 0%)
2025-08-02 17:23:13 [信息] 浏览器语言设置: 多线程模式使用指纹国家代码=AZ, 语言=Azərbaycan dili, 参数=--lang=az-AZ
2025-08-02 17:23:13 [信息] 随机选择User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36
2025-08-02 17:23:13 线程2：[信息] [信息] 尝试启动系统Chrome(无痕模式)... (进度: 0%)
2025-08-02 17:23:16 线程1：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-02 17:23:16 线程2：[信息] [信息] 创建无痕模式上下文... (进度: 0%)
2025-08-02 17:23:18 线程1：[信息] [信息] 多线程模式使用指纹时区: Asia/Baku (进度: 0%)
2025-08-02 17:23:18 [信息] 浏览器时区设置: 多线程模式使用指纹时区=Asia/Baku
2025-08-02 17:23:18 线程1：[信息] [信息] 多线程模式使用指纹语言: az-AZ,az;q=0.9,en;q=0.8 (进度: 0%)
2025-08-02 17:23:18 [信息] 浏览器语言设置: 多线程模式使用指纹语言=az-AZ,az;q=0.9,en;q=0.8
2025-08-02 17:23:18 线程1：[信息] [信息] 多线程模式使用指纹地理位置: 39.2078, 45.0017 (进度: 0%)
2025-08-02 17:23:18 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=39.2078, 经度=45.0017
2025-08-02 17:23:18 线程1：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_007, CPU: 20核 (进度: 0%)
2025-08-02 17:23:18 [信息] 浏览器指纹注入: Canvas=canvas_fp_007, WebGL=ANGLE (NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0), CPU=20核, RAM=48 GB
2025-08-02 17:23:18 线程2：[信息] [信息] 多线程模式使用指纹时区: Asia/Baku (进度: 0%)
2025-08-02 17:23:18 [信息] 浏览器时区设置: 多线程模式使用指纹时区=Asia/Baku
2025-08-02 17:23:18 线程2：[信息] [信息] 多线程模式使用指纹语言: az-AZ,az;q=0.9,en;q=0.8 (进度: 0%)
2025-08-02 17:23:18 [信息] 浏览器语言设置: 多线程模式使用指纹语言=az-AZ,az;q=0.9,en;q=0.8
2025-08-02 17:23:18 线程2：[信息] [信息] 多线程模式使用指纹地理位置: 38.4197, 48.8503 (进度: 0%)
2025-08-02 17:23:18 [信息] 浏览器地理位置设置: 多线程模式使用指纹地理位置, 纬度=38.4197, 经度=48.8503
2025-08-02 17:23:18 线程2：[信息] [信息] 已注入浏览器指纹伪装脚本 - Canvas: canvas_fp_002, CPU: 6核 (进度: 0%)
2025-08-02 17:23:18 [信息] 浏览器指纹注入: Canvas=canvas_fp_002, WebGL=ANGLE (AMD Radeon RX 6600 Direct3D11 vs_5_0 ps_5_0), CPU=6核, RAM=4 GB
2025-08-02 17:23:23 线程1：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-02 17:23:23 线程2：[信息] [信息] 已创建新的无痕模式上下文和页面 (进度: 0%)
2025-08-02 17:23:24 线程1：[信息] [信息] 指纹测试 - CPU核心数: 20 (进度: 0%)
2025-08-02 17:23:24 线程2：[信息] [信息] 指纹测试 - CPU核心数: 6 (进度: 0%)
2025-08-02 17:23:24 线程1：[信息] [信息] 指纹测试 - 设备内存: 48 (进度: 0%)
2025-08-02 17:23:24 线程2：[信息] [信息] 指纹测试 - 设备内存: 4 (进度: 0%)
2025-08-02 17:23:24 线程1：[信息] [信息] 指纹测试 - Do Not Track: manual (进度: 0%)
2025-08-02 17:23:24 线程2：[信息] [信息] 指纹测试 - Do Not Track: default (进度: 0%)
2025-08-02 17:23:24 线程1：[信息] [信息] 指纹测试 - WebGL信息: System.Dynamic.ExpandoObject (进度: 0%)
2025-08-02 17:23:24 线程2：[信息] [信息] 指纹测试 - WebGL信息: System.Dynamic.ExpandoObject (进度: 0%)
2025-08-02 17:23:24 线程1：[信息] [信息] 指纹测试 - Canvas指纹: ...1QAAAABJRUcanvas_f== (进度: 0%)
2025-08-02 17:23:24 线程1：[信息] [信息] 指纹测试 - 屏幕信息: System.Dynamic.ExpandoObject (进度: 0%)
2025-08-02 17:23:24 线程2：[信息] [信息] 指纹测试 - Canvas指纹: ...1QAAAABJRUcanvas_f== (进度: 0%)
2025-08-02 17:23:24 线程1：[信息] [信息] 指纹测试 - 语言信息: System.Dynamic.ExpandoObject (进度: 0%)
2025-08-02 17:23:24 线程2：[信息] [信息] 指纹测试 - 屏幕信息: System.Dynamic.ExpandoObject (进度: 0%)
2025-08-02 17:23:24 线程1：[信息] [信息] 指纹测试 - 时区偏移: -240分钟 (进度: 0%)
2025-08-02 17:23:24 线程2：[信息] [信息] 指纹测试 - 语言信息: System.Dynamic.ExpandoObject (进度: 0%)
2025-08-02 17:23:24 线程1：[信息] [信息] 指纹测试 - 设备信息: System.Dynamic.ExpandoObject (进度: 0%)
2025-08-02 17:23:24 线程1：[信息] [信息] 指纹测试 - 端口扫描保护测试完成 (进度: 0%)
2025-08-02 17:23:24 [信息] 指纹功能测试完成 - CPU: 20核, 内存: 48, DNT: manual, 时区偏移: -240, 设备: System.Dynamic.ExpandoObject
2025-08-02 17:23:24 线程2：[信息] [信息] 指纹测试 - 时区偏移: -240分钟 (进度: 0%)
2025-08-02 17:23:24 线程1：[信息] [信息] 已设置浏览器标题: 线程1 - AWS注册工具 (进度: 0%)
2025-08-02 17:23:24 线程1：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-02 17:23:24 线程1：[信息] 浏览器启动成功
2025-08-02 17:23:24 线程1：[信息] 获取下一个数据: <EMAIL>
2025-08-02 17:23:24 线程2：[信息] [信息] 指纹测试 - 设备信息: System.Dynamic.ExpandoObject (进度: 0%)
2025-08-02 17:23:24 线程1：[信息] 开始处理账户: <EMAIL>
2025-08-02 17:23:24 线程1：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-02 17:23:24 线程2：[信息] [信息] 指纹测试 - 端口扫描保护测试完成 (进度: 0%)
2025-08-02 17:23:24 [信息] 指纹功能测试完成 - CPU: 6核, 内存: 4, DNT: default, 时区偏移: -240, 设备: System.Dynamic.ExpandoObject
2025-08-02 17:23:24 线程1：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-02 17:23:24 线程1：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-02 17:23:24 线程1：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-02 17:23:24 线程1：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-02 17:23:24 线程2：[信息] [信息] 已设置浏览器标题: 线程2 - AWS注册工具 (进度: 0%)
2025-08-02 17:23:24 线程2：[信息] [信息] 本地浏览器启动成功 - 无痕模式检测: ✓ 已启用 (进度: 0%)
2025-08-02 17:23:24 线程2：[信息] 浏览器启动成功
2025-08-02 17:23:24 线程2：[信息] 获取下一个数据: <EMAIL>
2025-08-02 17:23:24 线程2：[信息] 开始处理账户: <EMAIL>
2025-08-02 17:23:24 线程2：[信息] [信息] 已清空AWS密钥变量和MFA变量 (进度: 98%)
2025-08-02 17:23:24 线程2：[信息] [信息] 开始新的注册: <EMAIL>，已清空之前的号码状态 (进度: 98%)
2025-08-02 17:23:24 线程2：[信息] [信息] 在现有窗口中新建标签页... (进度: 98%)
2025-08-02 17:23:24 线程2：[信息] [信息] 使用现有的浏览器上下文 (进度: 98%)
2025-08-02 17:23:24 线程2：[信息] [信息] 正在新建标签页... (进度: 98%)
2025-08-02 17:23:24 线程1：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-02 17:23:24 线程1：[信息] [信息] 已重新设置浏览器标题: 线程1 - AWS注册工具 (进度: 98%)
2025-08-02 17:23:24 线程1：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-02 17:23:24 线程1：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-02 17:23:24 线程1：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-02 17:23:24 线程1：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-02 17:23:24 线程2：[信息] [信息] 已设置页面视口大小为384x310 (进度: 98%)
2025-08-02 17:23:24 线程2：[信息] [信息] 已重新设置浏览器标题: 线程2 - AWS注册工具 (进度: 98%)
2025-08-02 17:23:24 线程2：[信息] [信息] 验证无痕Chrome模式状态... (进度: 98%)
2025-08-02 17:23:26 线程2：[信息] [信息] 无痕Chrome检测: ✓ 已启用 (进度: 98%)
2025-08-02 17:23:26 线程2：[信息] [信息] 浏览器信息: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWeb... (进度: 98%)
2025-08-02 17:23:26 线程2：[信息] [信息] 正在打开AWS注册页面... (进度: 98%)
2025-08-02 17:23:39 线程1：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程1 - AWS注册 (进度: 98%)
2025-08-02 17:23:39 线程1：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-02 17:23:39 线程1：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-02 17:23:39 线程1：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-02 17:23:39 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-02 17:23:39 线程1：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-02 17:23:42 线程1：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-02 17:23:42 线程1：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-02 17:23:42 线程1：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-02 17:23:42 线程1：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-02 17:23:42 线程1：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-02 17:23:42 线程1：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-02 17:23:42 线程1：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-02 17:23:42 线程1：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-02 17:23:42 线程1：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-02 17:23:42 线程1：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-02 17:23:45 线程1：[信息] [信息] 第一页已从iframe截取验证码图片，大小: 35667 字节 (进度: 100%)
2025-08-02 17:23:45 线程1：[信息] [信息] ✅ 图片验证通过：201x71px，35667字节，复杂度符合要求 (进度: 100%)
2025-08-02 17:23:45 线程1：[信息] [信息] 正在调用Yes打码API识别验证码... (进度: 100%)
2025-08-02 17:23:47 线程2：[信息] [信息] 已设置浏览器标题并启用自动保持: 线程2 - AWS注册 (进度: 98%)
2025-08-02 17:23:47 线程2：[信息] [信息] 正在执行第一页注册... (进度: 98%)
2025-08-02 17:23:47 线程2：[信息] [信息] 🔍 等待第一页加载完成... (进度: 98%)
2025-08-02 17:23:47 线程2：[信息] [信息] ✅ 第一页加载完成，找到验证邮箱按钮 (进度: 98%)
2025-08-02 17:23:47 [信息] 第一页加载完成，找到验证邮箱按钮
2025-08-02 17:23:48 线程2：[信息] [信息] 📋 第一页基本信息填写完成，检查页面响应... (进度: 98%)
2025-08-02 17:23:49 线程1：[信息] [信息] 🔍 Yes打码API响应内容: {"errorId":0,"errorCode":"","status":"ready","solution":{"text":"2hstf7"},"taskId":"663c7c02-6f82-11f0-bf95-5254008382c7"} (进度: 100%)
2025-08-02 17:23:49 线程1：[信息] [信息] 第一页第1次识别结果: 2hstf7 → 转换为小写: 2hstf7 (进度: 100%)
2025-08-02 17:23:49 线程1：[信息] [信息] 第一页使用iframe内GetByLabel选择器 (进度: 100%)
2025-08-02 17:23:49 线程1：[信息] [信息] 已填入验证码: 2hstf7 (进度: 100%)
2025-08-02 17:23:49 线程1：[信息] [信息] 已点击iframe内Submit按钮 (进度: 100%)
2025-08-02 17:23:51 线程2：[信息] [信息] 🔍 检查是否出现IP异常错误... (进度: 98%)
2025-08-02 17:23:51 线程2：[信息] [信息] ✅ 未检测到IP异常错误，继续流程 (进度: 100%)
2025-08-02 17:23:51 线程2：[信息] [信息] 🔍 开始检查第一页是否有图形验证码（2次检测）... (进度: 100%)
2025-08-02 17:23:51 线程2：[信息] [信息] 🔍 第1次检测图形验证码... (进度: 100%)
2025-08-02 17:23:51 线程2：[信息] [信息] 🔍 第1次检测 - Security对话框: 1个, Type文本: 0个, Verification输入框: 0个, Submit按钮: 0个, 总图片: 2个 (进度: 100%)
2025-08-02 17:23:51 线程2：[信息] [信息] ✅ 第1次检测发现图形验证码！ (进度: 100%)
2025-08-02 17:23:51 线程2：[信息] [信息] 🔍 第一页图形验证码最终检测结果: 发现验证码 (进度: 100%)
2025-08-02 17:23:51 线程2：[信息] [信息] ⚠️ 第一页检测到图形验证码，开始处理... (进度: 100%)
2025-08-02 17:23:51 线程2：[信息] [信息] 第一页图形验证码自动识别模式 (进度: 100%)
2025-08-02 17:23:51 线程2：[信息] [信息] 第一页第1次尝试自动识别图形验证码... (进度: 100%)
2025-08-02 17:23:51 线程1：[信息] [信息] 第一页第1次图形验证码识别成功 (进度: 100%)
2025-08-02 17:23:51 线程1：[信息] [信息] 第一页图形验证码自动完成 (进度: 100%)
2025-08-02 17:23:51 线程1：[信息] [信息] 📋 第一页图形验证码检查完成 (进度: 100%)
2025-08-02 17:23:51 线程1：[信息] [信息] 第一页完成，等待验证码页面... (进度: 100%)
2025-08-02 17:23:51 线程1：[信息] [信息] 邮箱验证码配置检查：自动模式=True，提供商=Microsoft，服务状态=True (进度: 100%)
2025-08-02 17:23:51 线程1：[信息] [信息] 邮箱验证码自动模式已启用（Microsoft），等待2秒后开始获取验证码... (进度: 100%)
2025-08-02 17:23:51 线程1：[信息] [信息] 正在后台自动获取验证码，继续注册按钮已禁用... (进度: 100%)
2025-08-02 17:23:51 线程1：[信息] 账户注册流程已启动: <EMAIL>
2025-08-02 17:23:51 线程1：[信息] 线程数据处理方法完成，注册流程可能仍在进行中
2025-08-02 17:23:51 [信息] [线程1] 开始邮箱验证码获取流程，邮箱: <EMAIL>
2025-08-02 17:23:51 [信息] [线程1] 等待2秒后开始第一次触发...
2025-08-02 17:23:52 [信息] 多线程窗口引用已清理
2025-08-02 17:23:52 [信息] 主窗口已恢复正常状态，多线程窗口引用已清理
2025-08-02 17:23:52 [信息] 多线程管理窗口正在关闭
2025-08-02 17:23:53 [信息] 程序正在退出，开始清理工作...
2025-08-02 17:23:53 [信息] 程序退出，开始清理所有待拉黑千川手机号码
2025-08-02 17:23:53 [信息] 程序退出清理完成: 没有需要拉黑的千川手机号码
2025-08-02 17:23:53 [信息] 程序退出，开始清理所有待释放手机号码
2025-08-02 17:23:53 [信息] 程序退出清理完成: 没有需要释放的手机号码
2025-08-02 17:23:53 [信息] 程序退出清理工作完成
